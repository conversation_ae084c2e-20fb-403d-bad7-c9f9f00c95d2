package cli

import (
	"strings"

	"github.com/spf13/cobra"

	"resumatter/pkg/config"
	"resumatter/pkg/formatter"
	"resumatter/pkg/service"
)

// App represents the CLI application
type App struct {
	service *service.Service
	config  *config.Config
}

// NewApp creates a new CLI application
func NewApp() (*App, error) {
	cfg := config.Load()

	svc, err := service.New(cfg)
	if err != nil {
		return nil, err
	}

	return &App{
		service: svc,
		config:  cfg,
	}, nil
}

// Close closes the application and its dependencies
func (a *App) Close() error {
	if a.service != nil {
		return a.service.Close()
	}
	return nil
}

// Execute executes the root command
func (a *App) Execute() error {
	defer a.Close()
	return a.rootCmd().Execute()
}

// rootCmd creates the root command
func (a *App) rootCmd() *cobra.Command {
	var outputFile string
	var outputFormat string

	cmd := &cobra.Command{
		Use:   "resumatter",
		Short: "AI-powered resume optimization tool",
		Long: `Resumatter is an AI-powered resume optimization tool that provides:
- Resume tailoring for specific job descriptions
- Resume evaluation for accuracy and consistency
- Job description analysis for quality and effectiveness`,
		SilenceUsage: true,
	}

	// Add persistent flags
	cmd.PersistentFlags().StringVarP(&outputFile, "output", "o", "", "Output file path (default: stdout)")
	cmd.PersistentFlags().StringVar(&outputFormat, "format", "text", "Output format: json or text")

	// Add commands
	cmd.AddCommand(a.tailorCmd(&outputFile, &outputFormat))
	cmd.AddCommand(a.evaluateCmd(&outputFile, &outputFormat))
	cmd.AddCommand(a.analyzeCmd(&outputFile, &outputFormat))

	return cmd
}

// parseOutputFormat parses and validates the output format
func (a *App) parseOutputFormat(format string) formatter.OutputFormat {
	switch strings.ToLower(format) {
	case "json":
		return formatter.FormatJSON
	default:
		return formatter.FormatText
	}
}
