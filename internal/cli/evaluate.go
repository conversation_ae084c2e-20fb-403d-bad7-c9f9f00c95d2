package cli

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
)

// evaluateCmd creates the evaluate command
func (a *App) evaluateCmd(outputFile, outputFormat *string) *cobra.Command {
	return &cobra.Command{
		Use:   "evaluate [base-resume-file] [tailored-resume-file]",
		Short: "Evaluate a tailored resume for accuracy and consistency",
		Long: `Evaluate a tailored resume against the original to detect potential
fabrications, exaggerations, or inconsistencies. This helps ensure the
tailored resume maintains accuracy while being optimized.`,
		Args: cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return a.runEvaluate(args[0], args[1], *outputFile, *outputFormat)
		},
	}
}

// runEvaluate executes the evaluate command
func (a *App) runEvaluate(baseResumeFile, tailoredResumeFile, outputFile, outputFormat string) error {
	fmt.Println("Evaluating resume...")

	ctx, cancel := context.WithTimeout(context.Background(), a.config.Timeout)
	defer cancel()

	result, err := a.service.EvaluateResume(ctx, baseResumeFile, tailoredResumeFile)
	if err != nil {
		return err
	}

	format := a.parseOutputFormat(outputFormat)
	return a.service.WriteOutput(result, format, outputFile)
}
