package cli

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
)

// analyzeCmd creates the analyze command
func (a *App) analyzeCmd(outputFile, outputFormat *string) *cobra.Command {
	return &cobra.Command{
		Use:   "analyze [job-description-file]",
		Short: "Analyze a job description for quality and effectiveness",
		Long: `Analyze a job description to assess its quality, clarity, inclusivity,
and effectiveness in attracting qualified candidates. Provides actionable
recommendations for improvement.`,
		Args: cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return a.runAnalyze(args[0], *outputFile, *outputFormat)
		},
	}
}

// runAnalyze executes the analyze command
func (a *App) runAnalyze(jobFile, outputFile, outputFormat string) error {
	fmt.Println("Analyzing job description...")

	ctx, cancel := context.WithTimeout(context.Background(), a.config.Timeout)
	defer cancel()

	result, err := a.service.AnalyzeJob(ctx, jobFile)
	if err != nil {
		return err
	}

	format := a.parseOutputFormat(outputFormat)
	return a.service.WriteOutput(result, format, outputFile)
}
