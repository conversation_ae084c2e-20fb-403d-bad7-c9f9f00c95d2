package cli

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
)

// tailorCmd creates the tailor command
func (a *App) tailorCmd(outputFile, outputFormat *string) *cobra.Command {
	return &cobra.Command{
		Use:   "tailor [resume-file] [job-description-file]",
		Short: "Tailor a resume for a specific job description",
		Long: `Tailor your resume for a specific job description using AI.
The command takes two arguments: the path to your base resume file and 
the path to the job description file. Both files should be in plain text format.`,
		Args: cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return a.runTailor(args[0], args[1], *outputFile, *outputFormat)
		},
	}
}

// runTailor executes the tailor command
func (a *App) runTailor(resumeFile, jobFile, outputFile, outputFormat string) error {
	fmt.Println("Tailoring resume...")

	ctx, cancel := context.WithTimeout(context.Background(), a.config.Timeout)
	defer cancel()

	result, err := a.service.TailorResume(ctx, resumeFile, jobFile)
	if err != nil {
		return err
	}

	format := a.parseOutputFormat(outputFormat)
	return a.service.WriteOutput(result, format, outputFile)
}
