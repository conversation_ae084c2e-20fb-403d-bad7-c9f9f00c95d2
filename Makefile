.PHONY: build clean test run-example help lint fmt vet

# Build configuration
BINARY_NAME = resumatter
BUILD_DIR = ./build
CMD_DIR = ./cmd/resumatter

# Default target
all: build

# Build the application
build: deps
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) $(CMD_DIR)
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -f coverage.*
	rm -f *.out
	go clean

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy
	@echo "Syncing workspace dependencies..."
	go work sync

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Vet code
vet:
	@echo "Vetting code..."
	go vet ./...

# Lint code (requires golangci-lint)
lint:
	@echo "Linting code..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not found, skipping lint check"; \
	fi

# Run tests
test:
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage
test-coverage: deps
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Run example tailor command (requires API key)
run-example:
	@echo "Running example tailor command..."
	@if [ -z "$$GEMINI_API_KEY" ] && [ -z "$$RESUMATTER_AI_APIKEY" ]; then \
		echo "Error: Please set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable"; \
		exit 1; \
	fi
	go run $(CMD_DIR) tailor examples/resume.txt examples/job.txt

# Run example with JSON output
run-example-json:
	@echo "Running example with JSON output..."
	@if [ -z "$$GEMINI_API_KEY" ] && [ -z "$$RESUMATTER_AI_APIKEY" ]; then \
		echo "Error: Please set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable"; \
		exit 1; \
	fi
	go run $(CMD_DIR) tailor examples/resume.txt examples/job.txt --format json

# Run evaluation example
run-evaluate:
	@echo "Running evaluation example..."
	@if [ -z "$$GEMINI_API_KEY" ] && [ -z "$$RESUMATTER_AI_APIKEY" ]; then \
		echo "Error: Please set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable"; \
		exit 1; \
	fi
	go run $(CMD_DIR) evaluate examples/resume.txt examples/resume.txt

# Run analysis example
run-analyze:
	@echo "Running analysis example..."
	@if [ -z "$$GEMINI_API_KEY" ] && [ -z "$$RESUMATTER_AI_APIKEY" ]; then \
		echo "Error: Please set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable"; \
		exit 1; \
	fi
	go run $(CMD_DIR) analyze examples/job.txt

# Install the binary to $GOPATH/bin
install: build
	@echo "Installing $(BINARY_NAME) to $$GOPATH/bin..."
	cp $(BUILD_DIR)/$(BINARY_NAME) $$GOPATH/bin/

# Development workflow
dev: fmt vet lint test

# Show help
help:
	@echo "Available targets:"
	@echo "  build           - Build the application"
	@echo "  clean           - Clean build artifacts"
	@echo "  deps            - Download and tidy dependencies"
	@echo "  fmt             - Format code"
	@echo "  vet             - Vet code"
	@echo "  lint            - Lint code (requires golangci-lint)"
	@echo "  test            - Run tests"
	@echo "  test-coverage   - Run tests with coverage"
	@echo "  run-example     - Run example tailor command"
	@echo "  run-example-json - Run example with JSON output"
	@echo "  run-evaluate    - Run evaluation example"
	@echo "  run-analyze     - Run analysis example"
	@echo "  install         - Install binary to GOPATH/bin"
	@echo "  dev             - Run development workflow (fmt, vet, lint, test)"
	@echo "  help            - Show this help message"
	@echo ""
	@echo "Environment variables:"
	@echo "  GEMINI_API_KEY or RESUMATTER_AI_APIKEY - Required for API access"
	@echo "  GEMINI_MODEL - Optional, defaults to gemini-2.0-flash"

